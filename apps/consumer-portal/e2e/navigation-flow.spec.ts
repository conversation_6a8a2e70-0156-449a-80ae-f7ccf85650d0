import { test, expect } from '@playwright/test';
import { loginToPortal, VIEWPORTS, navigateToPage, switchToTab } from './test-utils';

test.describe('Navigation Flow Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize(VIEWPORTS.desktop);
    await loginToPortal(page);
  });

  test('should navigate through all main sections', async ({ page }) => {
    // Navigate through all pages
    await navigateToPage(page, 'home');
    await page.screenshot({ path: 'test-results/navigation-01-home.png', fullPage: true });

    await navigateToPage(page, 'payments');
    await page.screenshot({ path: 'test-results/navigation-02-payments.png', fullPage: true });

    await navigateToPage(page, 'rewards');
    await page.screenshot({ path: 'test-results/navigation-03-rewards.png', fullPage: true });

    await navigateToPage(page, 'home');
  });

  test('should switch between tabs on home page', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Test tab switching with screenshots
    await switchToTab(page, 'recentActivity');
    await page.screenshot({ path: 'test-results/tabs-01-recent-activity.png', fullPage: true });

    await switchToTab(page, 'statements');
    await page.screenshot({ path: 'test-results/tabs-02-statements.png', fullPage: true });

    await switchToTab(page, 'recentActivity');
  });

});
