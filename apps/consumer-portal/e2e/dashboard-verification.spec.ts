import { test, expect } from '@playwright/test';
import {
  loginToPortal,
  VIEWPORTS,
  ELEMENTS,
  switchToTab,
  navigateToPage,
  verifyNavigation,
  verifyDashboardContent,
  downloadAndVerifyPdf
} from './test-utils';

test.describe('Dashboard UI Verification', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize(VIEWPORTS.desktop);
    await loginToPortal(page);
  });

  test('should display main navigation elements correctly', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await verifyNavigation(page);
  });

  test('should display home page tabs and allow switching', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Verify both tabs are visible
    const recentActivityTab = page.locator(ELEMENTS.tabs.recentActivity).first();
    const statementsTab = page.locator(ELEMENTS.tabs.statements).first();
    await expect(recentActivityTab).toBeVisible();
    await expect(statementsTab).toBeVisible();

    // Test tab switching
    await switchToTab(page, 'statements');
    await expect(statementsTab).toBeVisible();

    await switchToTab(page, 'recentActivity');
    await expect(recentActivityTab).toBeVisible();
  });

  test('should navigate to Payments page and display content', async ({ page }) => {
    await navigateToPage(page, 'payments');
    await expect(page.locator('body')).toBeVisible();
  });

  test('should navigate to Rewards page and display content', async ({ page }) => {
    await navigateToPage(page, 'rewards');
    await expect(page.locator('body')).toBeVisible();
  });

  test('should verify dashboard content structure and key elements', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await verifyDashboardContent(page);
  });

  test('should verify statements tab shows statement content', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    await switchToTab(page, 'statements');
    await expect(page.locator('body')).toBeVisible();

    // Look for statement-related content
    const statementsContent = await page.locator('body').textContent();
    expect(statementsContent).toMatch(/\d{4}/); // Should contain year
  });

  test('should verify PDF functionality', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    await switchToTab(page, 'statements');

    // Look for statement PDF links
    const statementPdfSelector = 'a[href*=".pdf"]';
    const hasStatementPdfs = await downloadAndVerifyPdf(page, statementPdfSelector, 'dashboard-statement.pdf');

    if (!hasStatementPdfs) {
      console.log('No statement PDF links found - test account may not have statements yet');
    }
  });



});
