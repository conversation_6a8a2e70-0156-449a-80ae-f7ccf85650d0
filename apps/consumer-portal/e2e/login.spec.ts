import { test, expect } from '@playwright/test';
import { loginToPortal, TEST_CREDENTIALS } from './test-utils';

test.describe('Cardholder Portal Login', () => {
  test('should successfully login with email credentials', async ({ page }) => {
    await loginToPortal(page);

    // Verify we're logged in and on a valid page
    await expect(page.locator('body')).toBeVisible();
    await expect(page).toHaveTitle('Tallied Cardholder Portal - Tallied Internal Program');
  });

  test('should display login page correctly', async ({ page }) => {
    await page.goto('/login');

    // Verify the page loads correctly
    await expect(page).toHaveTitle('Tallied Cardholder Portal - Tallied Internal Program');

    // Check that login button is visible
    const loginButton = page.locator('text=Login with email').or(
      page.locator('button:has-text("Login with email")')
    );
    await expect(loginButton).toBeVisible();
    await expect(page.locator('body')).toBeVisible();
  });
});
