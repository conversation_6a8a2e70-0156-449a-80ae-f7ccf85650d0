import { expect, Page } from '@playwright/test';
import * as fs from 'fs';

// Test credentials
export const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: '10Apples!'
};

// Viewport configurations
export const VIEWPORTS = {
  desktop: { width: 1920, height: 1080 },
  mobile: { width: 2868, height: 1320 } // iPhone 16 Pro Max
};

// Standard timeouts
export const TIMEOUTS = {
  short: 3000,
  medium: 5000,
  long: 8000,
  navigation: 10000
};

/**
 * Unified login function with consistent error handling
 */
export async function loginToPortal(page: Page): Promise<void> {
  await page.goto('/login');
  await expect(page).toHaveTitle('Tallied Cardholder Portal - Tallied Internal Program');

  // Click login button
  const loginButton = page.locator('text=Login with email').or(
    page.locator('button:has-text("Login with email")')
  );
  await expect(loginButton).toBeVisible({ timeout: TIMEOUTS.medium });
  await loginButton.click();

  // Wait for Zitadel login page - Firefox sometimes has issues
  try {
    await page.waitForLoadState('networkidle', { timeout: TIMEOUTS.long });
  } catch (error) {
    console.log('Networkidle timeout during login redirect, falling back to load state');
    await page.waitForLoadState('load', { timeout: TIMEOUTS.medium });
  }

  // Check for login page - Firefox sometimes shows "Error" title briefly
  const currentTitle = await page.title();
  if (currentTitle === 'Error') {
    console.log('Detected error page, waiting for proper login page...');
    await page.waitForTimeout(2000);
    await page.waitForLoadState('load');
  }

  await expect(page).toHaveTitle('Welcome Back!');

  // Enter username
  const usernameInput = page.locator('input[name="loginName"]');
  await expect(usernameInput).toBeVisible({ timeout: TIMEOUTS.medium });
  await usernameInput.fill(TEST_CREDENTIALS.email);

  // Click Next
  const nextButton = page.locator('button[type="submit"]');
  await expect(nextButton).toBeVisible({ timeout: TIMEOUTS.short });
  await nextButton.click();

  // Wait for password page
  await page.waitForLoadState('networkidle', { timeout: TIMEOUTS.medium });

  // Enter password
  const passwordInput = page.locator('input[type="password"]');
  await expect(passwordInput).toBeVisible({ timeout: TIMEOUTS.medium });
  await passwordInput.fill(TEST_CREDENTIALS.password);

  // Submit login
  const submitButton = page.locator('button[type="submit"]');
  await expect(submitButton).toBeVisible({ timeout: TIMEOUTS.short });
  await submitButton.click();

  // Wait for successful login - use different strategies for different browsers
  try {
    await page.waitForLoadState('networkidle', { timeout: TIMEOUTS.long });
  } catch (error) {
    // Firefox sometimes doesn't reach networkidle, so fallback to load state
    console.log('Networkidle timeout, falling back to load state');
    await page.waitForLoadState('load', { timeout: TIMEOUTS.medium });
  }

  await expect(page).not.toHaveURL(/\/login$/);
}

/**
 * Common page elements locators
 */
export const ELEMENTS = {
  navigation: {
    home: 'a:has-text("Home")',
    payments: 'a:has-text("Payments")',
    rewards: 'a:has-text("Rewards")'
  },
  tabs: {
    recentActivity: 'button:has-text("Recent Activity"), [role="tab"]:has-text("Recent Activity")',
    statements: 'button:has-text("Statements"), [role="tab"]:has-text("Statements")'
  },
  mobile: {
    hamburgerMenu: 'button[aria-label*="menu" i], button[aria-label*="navigation" i], .hamburger, .menu-toggle, button:has(.fa-bars), button:has(.menu-icon)'
  }
};

/**
 * Navigate to a specific page and verify
 */
export async function navigateToPage(page: Page, pageName: 'home' | 'payments' | 'rewards'): Promise<void> {
  // Ensure navigation is accessible (handles both desktop and mobile)
  await ensureNavigationAccessible(page);

  const navSelector = ELEMENTS.navigation[pageName];
  const navElement = page.locator(navSelector).first();

  await expect(navElement).toBeVisible({ timeout: TIMEOUTS.medium });
  await navElement.click();
  await page.waitForLoadState('networkidle');

  const expectedUrl = pageName === 'home' ? '/' : `/${pageName}`;
  await expect(page).toHaveURL(new RegExp(expectedUrl.replace('/', '\\/')));
}

/**
 * Switch to a specific tab on the home page
 */
export async function switchToTab(page: Page, tabName: 'recentActivity' | 'statements'): Promise<void> {
  const tabSelector = ELEMENTS.tabs[tabName];
  const tabElement = page.locator(tabSelector).first();

  await expect(tabElement).toBeVisible();
  await tabElement.click();
  await page.waitForLoadState('networkidle', { timeout: TIMEOUTS.medium });
}

/**
 * Detect if we're in mobile navigation mode (hamburger menu present)
 */
export async function isMobileNavigation(page: Page): Promise<boolean> {
  const hamburgerMenu = page.locator(ELEMENTS.mobile.hamburgerMenu).first();
  return await hamburgerMenu.isVisible();
}

/**
 * Handle mobile navigation (hamburger menu if present)
 */
export async function accessMobileNavigation(page: Page): Promise<void> {
  const hamburgerMenu = page.locator(ELEMENTS.mobile.hamburgerMenu).first();
  if (await hamburgerMenu.isVisible()) {
    await hamburgerMenu.click();
    await page.waitForTimeout(500); // Wait for menu animation
  }
}

/**
 * Smart navigation access - handles both desktop and mobile
 */
export async function ensureNavigationAccessible(page: Page): Promise<void> {
  // Firefox-friendly wait strategy
  try {
    await page.waitForLoadState('networkidle', { timeout: TIMEOUTS.medium });
  } catch (error) {
    console.log('Networkidle timeout in ensureNavigationAccessible, falling back to load state');
    await page.waitForLoadState('load', { timeout: TIMEOUTS.short });
  }

  // Check if we're in mobile mode and need to open hamburger menu
  if (await isMobileNavigation(page)) {
    await accessMobileNavigation(page);
  }

  // Give a moment for any animations to complete
  await page.waitForTimeout(300);
}

/**
 * Find statement PDFs specifically (visible in Statements tab)
 */
export async function findStatementPdfs(page: Page): Promise<any> {
  // Look for statement PDFs that contain date information and are visible
  const statementPdfSelectors = [
    'a[href*=".pdf"]:has-text("2024")',
    'a[href*=".pdf"]:has-text("2025")',
    'a[href*=".pdf"]:has-text("June")',
    'a[href*=".pdf"]:has-text("July")',
    'a[href*=".pdf"]:has-text("August")',
    'a[href*=".pdf"]:has-text("September")',
    'a[href*=".pdf"]:has-text("October")',
    'a[href*=".pdf"]:has-text("November")',
    'a[href*=".pdf"]:has-text("December")',
    'a[href*=".pdf"]:has-text("$")', // Statement PDFs often show amounts
  ];

  for (const selector of statementPdfSelectors) {
    const links = page.locator(selector);
    const count = await links.count();

    if (count > 0) {
      // Check if any of these links are actually visible
      for (let i = 0; i < count; i++) {
        const link = links.nth(i);
        if (await link.isVisible()) {
          console.log(`Found visible statement PDF with selector: ${selector}`);
          return link;
        }
      }
    }
  }

  return null;
}

/**
 * Simplified PDF verification - just check if link exists and is accessible
 */
export async function verifyPdfLink(page: Page, linkSelector: string): Promise<boolean> {
  // First try to find statement PDFs specifically
  const statementPdf = await findStatementPdfs(page);

  if (statementPdf) {
    await expect(statementPdf).toBeVisible();
    await expect(statementPdf).toBeEnabled();

    const pdfHref = await statementPdf.getAttribute('href');
    expect(pdfHref).toBeTruthy();
    expect(pdfHref).toContain('.pdf');

    const linkText = await statementPdf.textContent();
    console.log(`✓ Statement PDF link verified: "${linkText?.trim()}" -> ${pdfHref}`);
    return true;
  }

  // Fallback to generic PDF link search
  const pdfLinks = page.locator(linkSelector);
  const linkCount = await pdfLinks.count();

  if (linkCount === 0) {
    console.log('No PDF links found');
    return false;
  }

  const firstLink = pdfLinks.first();
  const isVisible = await firstLink.isVisible();

  if (!isVisible) {
    console.log('PDF link exists but is not visible (likely in footer/legal section)');
    return false;
  }

  await expect(firstLink).toBeEnabled();
  const pdfHref = await firstLink.getAttribute('href');
  expect(pdfHref).toBeTruthy();
  expect(pdfHref).toContain('.pdf');

  console.log(`✓ PDF link verified: ${pdfHref}`);
  return true;
}

/**
 * Simplified PDF verification - just check if link exists and is accessible
 */
export async function downloadAndVerifyPdf(page: Page, linkSelector: string, filename: string): Promise<boolean> {
  // First try to find statement PDFs specifically
  const statementPdf = await findStatementPdfs(page);

  if (statementPdf) {
    await expect(statementPdf).toBeVisible();
    await expect(statementPdf).toBeEnabled();

    const pdfHref = await statementPdf.getAttribute('href');
    if (!pdfHref || !pdfHref.includes('.pdf')) {
      console.log('Statement PDF link does not have valid href');
      return false;
    }

    const linkText = await statementPdf.textContent();
    console.log(`✓ Statement PDF link verified: "${linkText?.trim()}" -> ${pdfHref}`);

    // For simplicity, just verify the link exists and has correct href
    // Don't attempt download as it may open in new tab
    return true;
  }

  // Fallback to generic PDF link search
  const pdfLinks = page.locator(linkSelector);
  const linkCount = await pdfLinks.count();

  if (linkCount === 0) {
    console.log('No PDF links found for download');
    return false;
  }

  const firstLink = pdfLinks.first();
  const isVisible = await firstLink.isVisible();

  if (!isVisible) {
    console.log('PDF link exists but is not visible (likely in footer/legal section)');
    return false;
  }

  await expect(firstLink).toBeEnabled();
  const pdfHref = await firstLink.getAttribute('href');

  if (!pdfHref || !pdfHref.includes('.pdf')) {
    console.log('PDF link does not have valid href');
    return false;
  }

  console.log(`✓ PDF link verified: ${pdfHref}`);
  return true;
}

/**
 * Verify main dashboard content is present
 */
export async function verifyDashboardContent(page: Page): Promise<void> {
  const pageContent = await page.locator('body').textContent();

  // Essential content checks
  expect(pageContent).toContain('Current Balance');
  expect(pageContent).toContain('Available Credit');
  expect(pageContent).toContain('Rewards Balance');

  // Verify key elements are visible
  await expect(page.locator('text=Current Balance')).toBeVisible();
  await expect(page.locator('text=Available Credit')).toBeVisible();
  await expect(page.locator('text=Rewards Balance')).toBeVisible();
}

/**
 * Verify navigation elements are present and functional
 */
export async function verifyNavigation(page: Page): Promise<void> {
  // Ensure navigation is accessible (handles both desktop and mobile)
  await ensureNavigationAccessible(page);

  const homeNav = page.locator(ELEMENTS.navigation.home).first();
  const paymentsNav = page.locator(ELEMENTS.navigation.payments).first();
  const rewardsNav = page.locator(ELEMENTS.navigation.rewards).first();

  await expect(homeNav).toBeVisible({ timeout: TIMEOUTS.medium });
  await expect(paymentsNav).toBeVisible({ timeout: TIMEOUTS.medium });
  await expect(rewardsNav).toBeVisible({ timeout: TIMEOUTS.medium });

  // Verify links have correct hrefs
  await expect(homeNav).toHaveAttribute('href', '/');
  await expect(paymentsNav).toHaveAttribute('href', '/payments');
  await expect(rewardsNav).toHaveAttribute('href', '/rewards');
}

/**
 * Download PDF and verify its structure and content sections
 */
export async function downloadAndVerifyPdfStructure(page: Page, linkSelector: string): Promise<boolean> {
  const fs = require('fs');
  const path = require('path');

  // First try to find statement PDFs specifically
  const statementPdf = await findStatementPdfs(page);

  if (!statementPdf) {
    console.log('No statement PDF found');
    return false;
  }

  await expect(statementPdf).toBeVisible();
  await expect(statementPdf).toBeEnabled();

  const pdfHref = await statementPdf.getAttribute('href');
  if (!pdfHref || !pdfHref.includes('.pdf')) {
    console.log('Statement PDF link does not have valid href');
    return false;
  }

  console.log(`Downloading PDF from: ${pdfHref}`);

  try {
    // Start waiting for download before clicking
    const downloadPromise = page.waitForEvent('download', { timeout: 10000 });

    // Click the PDF link
    await statementPdf.click();

    // Wait for download to complete
    const download = await downloadPromise;

    // Save the file to a temporary location
    const downloadPath = path.join(__dirname, 'temp-statement.pdf');
    await download.saveAs(downloadPath);

    console.log(`PDF downloaded to: ${downloadPath}`);

    // Verify the file exists and has content
    if (!fs.existsSync(downloadPath)) {
      console.log('Downloaded PDF file does not exist');
      return false;
    }

    const stats = fs.statSync(downloadPath);
    if (stats.size === 0) {
      console.log('Downloaded PDF file is empty');
      return false;
    }

    console.log(`PDF file size: ${stats.size} bytes`);

    // Use pdf-parse to extract text content
    const pdfParse = require('pdf-parse');
    const pdfBuffer = fs.readFileSync(downloadPath);
    const pdfData = await pdfParse(pdfBuffer);

    const pdfText = pdfData.text;
    console.log('PDF text extracted, length:', pdfText.length);

    // Verify PDF structure and sections
    const structureValid = verifyPdfSections(pdfText);

    // Clean up temporary file
    try {
      fs.unlinkSync(downloadPath);
    } catch (e) {
      console.log('Could not delete temporary PDF file:', e);
    }

    return structureValid;

  } catch (error) {
    console.log('Error downloading or processing PDF:', error);
    return false;
  }
}

/**
 * Verify that the PDF contains the expected sections and elements
 */
function verifyPdfSections(pdfText: string): boolean {
  console.log('Verifying PDF sections and structure...');

  // Define expected sections and their elements
  const expectedSections = {
    'Account Summary': [
      'Previous Monthly Balance',
      'Payments & Credits',
      'Purchases',
      'Fees Charged',
      'Interest Charged',
      'New Balance',
      'Credit Limit',
      'Available Credit',
      'Statement Closing Date',
      'Days in Billing Cycle'
    ],
    'Payment Information': [
      'New Balance',
      'Past Due Amount',
      'Minimum Payment Due',
      'Payment Due Date',
      'Late Payment Warning',
      'Minimum Payment Warning'
    ]
  };

  let allSectionsValid = true;

  // Check each section
  for (const [sectionName, elements] of Object.entries(expectedSections)) {
    console.log(`\nChecking section: ${sectionName}`);

    // Verify section header exists
    if (!pdfText.includes(sectionName)) {
      console.log(`❌ Section header "${sectionName}" not found in PDF`);
      allSectionsValid = false;
      continue;
    }

    console.log(`✓ Section header "${sectionName}" found`);

    // Find the section in the text
    const sectionIndex = pdfText.indexOf(sectionName);
    const nextSectionIndex = findNextSectionIndex(pdfText, sectionIndex, Object.keys(expectedSections));
    const sectionText = nextSectionIndex > -1
      ? pdfText.substring(sectionIndex, nextSectionIndex)
      : pdfText.substring(sectionIndex);

    // Check each element within this section
    let sectionElementsValid = true;
    for (const element of elements) {
      if (sectionText.includes(element)) {
        console.log(`  ✓ "${element}" found under ${sectionName}`);
      } else {
        console.log(`  ❌ "${element}" NOT found under ${sectionName}`);
        sectionElementsValid = false;
        allSectionsValid = false;
      }
    }

    if (sectionElementsValid) {
      console.log(`✓ All elements verified for section: ${sectionName}`);
    }
  }

  // Additional basic structure checks
  console.log('\nChecking basic PDF structure...');

  const basicElements = [
    'Tallied',
    'Account Number:',
    'Tallied Card Customer'
  ];

  for (const element of basicElements) {
    if (pdfText.includes(element)) {
      console.log(`✓ Basic element "${element}" found`);
    } else {
      console.log(`❌ Basic element "${element}" NOT found`);
      allSectionsValid = false;
    }
  }

  return allSectionsValid;
}

/**
 * Helper function to find the start of the next section
 */
function findNextSectionIndex(text: string, currentIndex: number, sectionNames: string[]): number {
  let nextIndex = -1;

  for (const sectionName of sectionNames) {
    const index = text.indexOf(sectionName, currentIndex + 1);
    if (index > currentIndex && (nextIndex === -1 || index < nextIndex)) {
      nextIndex = index;
    }
  }

  return nextIndex;
}
