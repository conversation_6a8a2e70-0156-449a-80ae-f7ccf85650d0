import { test, expect } from '@playwright/test';
import { loginToPortal, VIEWPORTS, switchToTab, downloadAndVerifyPdf, verifyPdfLink } from './test-utils';

test.describe('PDF Content Verification', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize(VIEWPORTS.desktop);
    await loginToPortal(page);
  });

  test('should verify statement PDF download and structure', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    await switchToTab(page, 'statements');

    // Look for statement PDF links with date information
    const statementPdfSelector = 'a[href*=".pdf"]';
    const hasStatementPdfs = await downloadAndVerifyPdf(page, statementPdfSelector, 'pdf-verification-statement.pdf');

    if (!hasStatementPdfs) {
      console.log('No statement PDF links found - test account may not have statements yet');
    }
  });

  test('should verify PDF links are present and accessible', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    await switchToTab(page, 'statements');

    // Look for statement PDF links
    const statementPdfSelector = 'a[href*=".pdf"]';
    const hasValidPdfLinks = await verifyPdfLink(page, statementPdfSelector);

    if (!hasValidPdfLinks) {
      console.log('No PDF links found - test account may not have statements yet');
    }
  });
});
