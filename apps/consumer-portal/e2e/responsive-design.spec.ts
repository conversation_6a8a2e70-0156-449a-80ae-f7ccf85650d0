import { test, expect } from '@playwright/test';
import {
  loginToPortal,
  VIEWPORTS,
  ELEMENTS,
  navigateToPage,
  switchToTab,
  accessMobileNavigation,
  verifyNavigation,
  downloadAndVerifyPdf
} from './test-utils';

test.describe('Responsive Design Tests - Desktop (1920x1080)', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize(VIEWPORTS.desktop);
    await loginToPortal(page);
  });

  test('should display desktop navigation correctly', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await verifyNavigation(page);
  });

  test('should navigate between all pages on desktop', async ({ page }) => {
    await navigateToPage(page, 'home');
    await navigateToPage(page, 'payments');
    await navigateToPage(page, 'rewards');
    await navigateToPage(page, 'home');
  });

  test('should display and switch tabs on desktop', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    await switchToTab(page, 'statements');
    await switchToTab(page, 'recentActivity');

  });

  test('should verify PDF functionality on desktop', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    await switchToTab(page, 'statements');

    const statementPdfSelector = 'a[href*=".pdf"]';
    const hasStatementPdfs = await downloadAndVerifyPdf(page, statementPdfSelector, 'desktop-statement.pdf');

    if (!hasStatementPdfs) {
      console.log('No statement PDF links found for desktop test');
    }
  });
});

test.describe('Responsive Design Tests - Mobile (2868x1320)', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize(VIEWPORTS.mobile);
    await loginToPortal(page);
  });

  test('should display mobile navigation with hamburger menu', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Verify we're in mobile mode and can access navigation
    await verifyNavigation(page);
  });

  test('should navigate between pages on mobile', async ({ page }) => {
    // Navigation functions now handle mobile hamburger menu automatically
    await navigateToPage(page, 'home');
    await navigateToPage(page, 'payments');
    await navigateToPage(page, 'rewards');
    await navigateToPage(page, 'home');
  });

  test('should display and switch tabs on mobile', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    await switchToTab(page, 'statements');
    await switchToTab(page, 'recentActivity');
  });

  test('should verify PDF functionality on mobile', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    await switchToTab(page, 'statements');

    const statementPdfSelector = 'a[href*=".pdf"]';
    const hasStatementPdfs = await downloadAndVerifyPdf(page, statementPdfSelector, 'mobile-statement.pdf');

    if (!hasStatementPdfs) {
      console.log('No statement PDF links found for mobile test');
    }
  });
});

test.describe('Responsive Design Tests - Viewport Switching', () => {
  test.beforeEach(async ({ page }) => {
    await loginToPortal(page);
  });

  test('should handle viewport changes correctly', async ({ page }) => {
    // Start with desktop viewport
    await page.setViewportSize(VIEWPORTS.desktop);
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Verify desktop navigation is visible
    const homeNavDesktop = page.locator(ELEMENTS.navigation.home).first();
    await expect(homeNavDesktop).toBeVisible();

    // Switch to mobile viewport
    await page.setViewportSize(VIEWPORTS.mobile);
    await page.waitForTimeout(1000); // Wait for responsive changes

    // Navigation should adapt to mobile layout
    const hamburgerMenu = page.locator(ELEMENTS.mobile.hamburgerMenu).first();
    const homeNavMobile = page.locator(ELEMENTS.navigation.home).first();

    const hasHamburgerMenu = await hamburgerMenu.isVisible();
    const hasDirectNav = await homeNavMobile.isVisible();

    // Either hamburger menu should be visible OR direct navigation should be visible
    expect(hasHamburgerMenu || hasDirectNav).toBe(true);

    // Switch back to desktop viewport
    await page.setViewportSize(VIEWPORTS.desktop);
    await page.waitForTimeout(1000); // Wait for responsive changes

    // Desktop navigation should be restored
    const homeNavRestored = page.locator(ELEMENTS.navigation.home).first();
    await expect(homeNavRestored).toBeVisible();
  });
});
